import pandas as pd
import numpy as np
from model_script import *
from utilts import *
from tqdm import tqdm 

filled_df=pd.read_csv("筛选后协变量特征值列表.csv")
print(filled_df.shape)
# 按location分组
grouped = filled_df.groupby('location')
# 创建字典，key是location，value是对应的分组数据框
result_dict = {location: group for location, group in grouped}
# 打印结果
for location, group in result_dict.items():
    print(f"Location: {location}")
    print(group)
    break

# 基本结构查看
print("数据类型:", type(result_dict))
print("国家数量:", len(result_dict))
print("\n所有键(国家):", list(result_dict.keys()))
# 查看详细内容
print("\n完整数据内容:")
j=0
for country, data in result_dict.items():
    print(f"\n国家: {country}")
    print(f"数据: {data.iloc[:10,:10]}")
    j+=1
    if j>=5:
        break

def scale_result_dict(result_dict, outlier_threshold=1.5, handle_outliers='cap'):
    """
    将 result_dict 中所有国家的特征数据标准化到 [0, 1] 范围，并在标准化前检测异常值
    :param result_dict: 输入的原始数据字典
    :param outlier_threshold: IQR 倍数，用于定义异常值阈值（默认 1.5）
    :param handle_outliers: 处理异常值的方式，'cap'（限制到边界）、'remove'（移除）、'keep'（保留）
    :return: 标准化后的新字典 result_dict_scale
    """
    result_dict_scale = {}
    scaler_dict = {} 
    
    for country, df in result_dict.items():
        scaler = MinMaxScaler()
        features = df.iloc[:, 2:].copy()
        
        # 检测和处理异常值
        if handle_outliers in ['cap', 'remove']:
            for col in features.columns:
                # 计算四分位距 (IQR)
                Q1 = features[col].quantile(0.25)
                Q3 = features[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - outlier_threshold * IQR
                upper_bound = Q3 + outlier_threshold * IQR

                # 检测异常值
                outliers = (features[col] < lower_bound) | (features[col] > upper_bound)
                if outliers.any():
                    print(f"Country {country}, Column {col}: Detected {outliers.sum()} outliers")
                
                # 处理异常值
                if handle_outliers == 'cap':
                    # 限制到边界值
                    features.loc[features[col] < lower_bound, col] = lower_bound
                    features.loc[features[col] > upper_bound, col] = upper_bound
                elif handle_outliers == 'remove':
                    # 移除包含异常值的行（注意：这会影响整个 DataFrame）
                    features = features[~outliers]
                    # 如果移除了行，同步更新 location 和 year
                    df = df.loc[features.index]

        # 标准化到 [0, 1]
        scaled_features = scaler.fit_transform(features)
        
        # ==================== 修改 2：保存这个 scaler ====================
        scaler_dict[country] = scaler

        scaled_df = pd.DataFrame(scaled_features, columns=features.columns)
        scaled_df.insert(0, 'location', df['location'].values)
        scaled_df.insert(1, 'year', df['year'].values)
        result_dict_scale[country] = scaled_df
    
    # ==================== 修改 3：返回两个结果 ====================
    return result_dict_scale, scaler_dict

result_dict_scale, scaler_dict_global = scale_result_dict(result_dict, outlier_threshold=10, handle_outliers='cap')

import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv
from torch_geometric.data import Data
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import pandas as pd
import random

def set_seed(seed):
    """
    设置随机种子以确保结果可复现
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)  # if you are using multi-GPU.
        # 确保 cudnn 的确定性，可能会牺牲一些性能
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

def preprocess_data(result_dict, years=range(1960, 2020), exclude_cols=['location', 'year']):
    countries = list(result_dict.keys())
    sample_df = result_dict[countries[0]]
    feature_cols = [col for col in sample_df.columns if col not in exclude_cols]
    num_features = len(feature_cols) * len(years)
    features = np.zeros((len(countries), num_features))
    for i, country in enumerate(countries):
        df = result_dict[country]
        for j, year in enumerate(years):
            for k, col in enumerate(feature_cols):
                value = df[df['year'] == float(year)][col].values
                features[i, j * len(feature_cols) + k] = value[0] if len(value) > 0 else 0
    features = (features - features.mean(axis=0)) / (features.std(axis=0) + 1e-8)
    return torch.tensor(features, dtype=torch.float), countries, feature_cols

def build_graph(features, k=5):
    sim_matrix = cosine_similarity(features.numpy())
    edge_index = []
    for i in range(len(sim_matrix)):
        neighbors = np.argsort(sim_matrix[i])[::-1][1:k+1]
        for j in neighbors:
            edge_index.append([i, j])
            edge_index.append([j, i])
    edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
    return edge_index

class GCNModel(torch.nn.Module):
    def __init__(self, in_channels, hidden_channels, out_channels):
        super(GCNModel, self).__init__()
        self.conv1 = GCNConv(in_channels, hidden_channels)
        self.conv2 = GCNConv(hidden_channels, out_channels)
    
    def forward(self, data):
        x, edge_index = data.x, data.edge_index
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=0.5, training=self.training)
        x = self.conv2(x, edge_index)
        return x

def train_gcn(data, model, epochs=300, lr=0.005, temperature=0.07, neg_samples=10):
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=5e-4)
    model.train()
    for epoch in range(epochs):
        optimizer.zero_grad()
        out = model(data)
        loss = 0
        num_nodes = out.size(0)
        for i, j in data.edge_index.t():
            pos_sim = F.cosine_similarity(out[i], out[j], dim=0) / temperature
            neg_idx = torch.randint(0, num_nodes, (neg_samples,), device=out.device)
            neg_sim = F.cosine_similarity(out[i].unsqueeze(0), out[neg_idx]).mean() / temperature
            loss += -torch.log(torch.exp(pos_sim) / (torch.exp(pos_sim) + torch.exp(neg_sim)))
        loss = loss / data.edge_index.size(1)
        reg_loss = torch.norm(out, p=2).mean() * 0.001
        total_loss = loss + reg_loss
        total_loss.backward()
        optimizer.step()
        if epoch % 20 == 0:
            print(f"Epoch {epoch}, Loss: {total_loss.item():.4f}")
    return out

def get_embeddings(model, data):
    model.eval()
    with torch.no_grad():
        embeddings = model(data)
    return embeddings

def main(result_dict):
    set_seed(2025)
    years = range(1960, 2020)
    hidden_channels = 64
    out_channels = 32
    k_neighbors = 5
    print("Step 1: Preprocessing data with all features...")
    features, countries, feature_cols = preprocess_data(result_dict, years)
    in_channels = features.shape[1]
    print(f"Features shape: {features.shape}")
    print(f"Feature columns included: {feature_cols}")
    print("Step 2: Building graph with k-nearest neighbors...")
    edge_index = build_graph(features, k=k_neighbors)
    data = Data(x=features, edge_index=edge_index)
    print(f"Number of edges: {edge_index.size(1)}")
    print("Step 3: Initializing GCN model...")
    model = GCNModel(in_channels=in_channels, hidden_channels=hidden_channels, out_channels=out_channels)
    print("Step 4: Training GCN model with optimized InfoNCE loss...")
    embeddings = train_gcn(data, model, epochs=300)
    print("Step 5: Getting embeddings...")
    embeddings = get_embeddings(model, data)
    print(f"Embeddings shape: {embeddings.shape}")
    embeddings_np = embeddings.numpy()
    print("Embedding std (per dimension):", embeddings_np.std(axis=0).mean())
    norms = np.linalg.norm(embeddings_np, axis=1)
    print("Embedding norms (first 5):", norms[:5])
    embedding_dict = {country: embeddings[i].numpy() for i, country in enumerate(countries)}
    print("\nSample embeddings:")
    for country in countries[:3]:
        print(f"{country}: {embedding_dict[country][:5]}...")
    return embedding_dict

# 手动调用 main 函数来训练GCN并生成嵌入
embedding_dict = main(result_dict_scale)

cor_space_country=pd.DataFrame.from_dict(embedding_dict)
cor_space_country=cor_space_country.T
cor_space_country.to_csv("所有国家空间协变量emb.csv")

cor_space_country.head()

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt

def compute_similarity_matrix(embedding_dict):
    countries = list(embedding_dict.keys())
    embeddings = np.array([embedding_dict[country] for country in countries])
    sim_matrix = cosine_similarity(embeddings)
    return sim_matrix, countries

def find_similar_pairs(sim_matrix, countries, top_n=10, min_regions=4):
    num_countries = len(countries)
    sim_pairs = []
    region_dict = {
        'Africa': ['Burkina Faso', 'Algeria', 'Kenya', 'Nigeria'],
        'Europe': ['Poland', 'Albania', 'Germany', 'France'],
        'Americas': ['Grenada', 'Colombia', 'Canada', 'United States of America'],
        'Asia': ['Afghanistan', 'China', 'India', 'Japan'],
        'Oceania': ['Australia', 'New Zealand']
    }
    country_to_region = {}
    for region, region_countries in region_dict.items():
        for country in region_countries:
            if country in countries:
                country_to_region[country] = region
    for i in range(num_countries):
        for j in range(i + 1, num_countries):
            sim_pairs.append((countries[i], countries[j], sim_matrix[i, j]))
    sim_pairs = sorted(sim_pairs, key=lambda x: x[2], reverse=True)
    selected_pairs = []
    covered_regions = set()
    for pair in sim_pairs:
        c1, c2, sim = pair
        r1 = country_to_region.get(c1, 'Other')
        r2 = country_to_region.get(c2, 'Other')
        if (len(covered_regions) < min_regions or sim > 0.99) and len(selected_pairs) < top_n:
            selected_pairs.append(pair)
            covered_regions.add(r1)
            covered_regions.add(r2)
        if len(selected_pairs) >= top_n and len(covered_regions) >= min_regions:
            break
    return selected_pairs

def visualize_embeddings(embedding_dict, sim_pairs):
    countries = list(embedding_dict.keys())
    embeddings = np.array([embedding_dict[country] for country in countries])
    tsne = TSNE(n_components=2, random_state=42, perplexity=30)
    embeddings_2d = tsne.fit_transform(embeddings)
    plt.figure(figsize=(24, 20))
    plt.scatter(embeddings_2d[:, 0], embeddings_2d[:, 1], c='lightgray', s=30, alpha=0.5, label='Countries')
    colors = plt.cm.tab10(np.linspace(0, 1, len(sim_pairs)))
    for idx, (c1, c2, sim) in enumerate(sim_pairs):
        i1 = countries.index(c1)
        i2 = countries.index(c2)
        plt.scatter(embeddings_2d[i1, 0], embeddings_2d[i1, 1], c=[colors[idx]], s=100, label=f'{c1} (sim={sim:.3f})')
        plt.scatter(embeddings_2d[i2, 0], embeddings_2d[i2, 1], c=[colors[idx]], s=100)
        plt.plot([embeddings_2d[i1, 0], embeddings_2d[i2, 0]], 
                 [embeddings_2d[i1, 1], embeddings_2d[i2, 1]], 
                 c=colors[idx], linestyle='--', alpha=0.7)
    for i, country in enumerate(countries):
        plt.annotate(country, 
                     (embeddings_2d[i, 0], embeddings_2d[i, 1]), 
                     fontsize=8, 
                     ha='right', 
                     va='bottom', 
                     alpha=0.7, 
                     color='black',
                     xytext=(5, 5), 
                     textcoords='offset points')
    plt.title("t-SNE Visualization of Country Embeddings (All Countries Labeled)", fontsize=16)
    plt.xlabel("t-SNE Component 1", fontsize=12)
    plt.ylabel("t-SNE Component 2", fontsize=12)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    plt.tight_layout()
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.show()

def visualize_embedding_results(embedding_dict, top_n=10):
    print("Step 1: Computing similarity matrix...")
    sim_matrix, countries = compute_similarity_matrix(embedding_dict)
    print(f"Step 2: Finding top {top_n} similar country pairs with diverse regions...")
    top_sim_pairs = find_similar_pairs(sim_matrix, countries, top_n=top_n, min_regions=4)
    print(f"\nTop {top_n} similar country pairs:")
    for c1, c2, sim in top_sim_pairs:
        print(f"{c1} - {c2}: Cosine Similarity = {sim:.4f}")
    print("Step 3: Visualizing embeddings with t-SNE...")
    visualize_embeddings(embedding_dict, top_sim_pairs)

if __name__ == "__main__":
    visualize_embedding_results(embedding_dict, top_n=10)

# --- 提取并保存t-SNE坐标 ---

# 1. 准备数据 (请确保 embedding_dict 这个变量已经创建并包含了GCN的结果)
countries = list(embedding_dict.keys())
embeddings = np.array([embedding_dict[country] for country in countries])

# 2. 进行 t-SNE 计算以获取坐标
#    确保这里的参数与你 visualize_embeddings 函数中的完全一致，以保证结果相同
print("正在进行t-SNE降维计算...")
tsne = TSNE(n_components=2, random_state=42, perplexity=30)
embeddings_2d = tsne.fit_transform(embeddings)
print("计算完成。")

# 3. 创建一个包含国家名和对应坐标的 DataFrame
tsne_df = pd.DataFrame({
    'Country': countries,
    'TSNE_1': embeddings_2d[:, 0],  # X 轴坐标
    'TSNE_2': embeddings_2d[:, 1]   # Y 轴坐标
})

# 4. 将结果保存为 CSV 文件
output_filename = "tsne图结果坐标系.csv"
tsne_df.to_csv(output_filename, index=False)

print(f"t-SNE坐标已成功保存到 '{output_filename}'")
print("\n文件内容预览:")
print(tsne_df.head())

####DBSCAN聚类算法
import pandas as pd
import numpy as np
from sklearn.manifold import TSNE
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

# --- 步骤 1: 准备数据 ---
# 请确保在你运行这个单元格之前，`embedding_dict` 变量已经包含了GCN模型的结果。
print("正在准备数据...")
countries = list(embedding_dict.keys())
embeddings_32d = np.array([embedding_dict[country] for country in countries])

# --- 步骤 2: 进行 t-SNE 降维计算 ---
# 这里的参数与你之前的可视化代码完全一致，以保证结果相同。
print("正在进行t-SNE降维计算...")
tsne = TSNE(n_components=2, random_state=42, perplexity=30, n_iter=1000)
embeddings_2d = tsne.fit_transform(embeddings_32d)
print("t-SNE计算完成。")

# --- 步骤 3: 使用DBSCAN进行聚类 ---
# 在使用DBSCAN之前，对数据进行标准化是一个好习惯，可以使eps参数更直观。
embeddings_2d_scaled = StandardScaler().fit_transform(embeddings_2d)

# 初始化并运行DBSCAN算法。
# eps: 定义了邻域的半径。值越大，越容易形成大的簇。
# min_samples: 定义了形成一个簇所需要的最少样本数。
print("正在进行DBSCAN聚类...")
dbscan = DBSCAN(eps=0.35, min_samples=5)
cluster_labels = dbscan.fit_predict(embeddings_2d_scaled)
print("聚类完成。")

# --- 步骤 4: 创建并保存最终结果 ---
# 将国家名、t-SNE坐标和聚类标签（组号）合并到一个DataFrame中。
# 聚类标签为 -1 代表该点是离群点（噪声），不属于任何一个簇。
result_df = pd.DataFrame({
    'Country': countries,
    'TSNE_1': embeddings_2d[:, 0],  # X 轴坐标
    'TSNE_2': embeddings_2d[:, 1],  # Y 轴坐标
    'Cluster': cluster_labels       # 聚类组号
})

# 将结果保存为CSV文件
output_filename = "country_clusters.csv"
result_df.to_csv(output_filename, index=False)

# --- 步骤 5: 打印汇总信息和结果预览 ---
n_clusters = len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0)
n_noise = list(cluster_labels).count(-1)

print("\n--- 聚类结果汇总 ---")
print(f"发现的簇（组）数量: {n_clusters}")
print(f"被识别为噪声点的国家数量: {n_noise}")

print(f"\n聚类结果已成功保存到 '{output_filename}'")
print("\n文件内容预览:")
print(result_df.head())

# (可选) 绘制一个简单的、按簇着色的图来预览效果
plt.figure(figsize=(12, 10))
unique_labels = set(cluster_labels)
colors = [plt.cm.Spectral(each) for each in np.linspace(0, 1, len(unique_labels))]

for k, col in zip(unique_labels, colors):
    if k == -1:
        # 将噪声点用黑色表示
        col = [0, 0, 0, 1]

    class_member_mask = (cluster_labels == k)
    
    xy = embeddings_2d[class_member_mask]
    plt.plot(xy[:, 0], xy[:, 1], 'o', markerfacecolor=tuple(col),
             markeredgecolor='k', markersize=8, label=f'Cluster {k}')

plt.title(f'DBSCAN聚类结果 (共 {n_clusters} 个簇)')
plt.legend()
plt.grid(True)
plt.show()



from model_script import *
from utilts import *
from tqdm import tqdm 
import pandas as pd
import numpy as np
import torch
import gpytorch
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import MinMaxScaler
import gc

filled_df=pd.read_csv("筛选后协变量特征值列表.csv")
print(filled_df.shape)
# 按location分组
grouped = filled_df.groupby('location')
# 创建字典，key是location，value是对应的分组数据框
result_dict = {location: group for location, group in grouped}
# 打印结果
for location, group in result_dict.items():
    print(f"Location: {location}")
    print(group)
    break
cor_space_country = pd.read_csv("所有国家空间协变量emb.csv", index_col=0)

def split_train_test_years(df, test_size=-5):
    """划分训练和测试年份"""
    years = df['year'].sort_values().unique()
    if abs(test_size) >= len(years):
        raise ValueError("Test size must be smaller than the total number of years")
    test_years = years[test_size:].tolist()
    train_years = years[:test_size].tolist()
    return train_years, test_years

def get_train_test_data(df, train_years, test_years):
    """根据年份划分训练和测试数据集"""
    train_df = df[df['year'].isin(train_years)]
    test_df = df[df['year'].isin(test_years)]
    return train_df, test_df

def standardize_data(df, feature_start_idx=2):
    """标准化特征数据"""
    scaler = MinMaxScaler()
    features_scaled = scaler.fit_transform(df.iloc[:, feature_start_idx:])
    return pd.DataFrame(features_scaled, columns=df.iloc[:, feature_start_idx:].columns), scaler

def create_gp_model(X_train, y_train, device):
    """创建并返回高斯过程模型和似然函数，适应时间+空间嵌入输入"""
    class MultitaskGPModel(gpytorch.models.ExactGP):
        def __init__(self, train_x, train_y, likelihood):
            super(MultitaskGPModel, self).__init__(train_x, train_y, likelihood)
            self.mean_module = gpytorch.means.MultitaskMean(
                gpytorch.means.ConstantMean(), num_tasks=y_train.shape[1]
            )
            self.covar_module = gpytorch.kernels.MultitaskKernel(
                gpytorch.kernels.ScaleKernel(
                    gpytorch.kernels.ProductKernel(
                        gpytorch.kernels.RBFKernel(active_dims=[0]),  # 时间维度
                        gpytorch.kernels.RBFKernel(active_dims=range(1, 33))  # 嵌入维度 (32 维)
                    )
                ),
                num_tasks=y_train.shape[1], rank=1
            )

        def forward(self, x):
            mean_x = self.mean_module(x)
            covar_x = self.covar_module(x)
            return gpytorch.distributions.MultitaskMultivariateNormal(mean_x, covar_x)

    likelihood = gpytorch.likelihoods.MultitaskGaussianLikelihood(num_tasks=y_train.shape[1]).to(device)
    model = MultitaskGPModel(X_train, y_train, likelihood).to(device)
    return model, likelihood

def train_model(model, likelihood, X_train, y_train, training_iter=80, lr=0.1):
    """训练高斯过程模型"""
    model.train()
    likelihood.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    mll = gpytorch.mlls.ExactMarginalLogLikelihood(likelihood, model)

    # 【修改这里】为训练迭代也加上tqdm
    # leave=False 表示这个内部进度条完成后会消失，保持界面干净
    for i in tqdm(range(training_iter), desc="模型训练中", leave=False):
        optimizer.zero_grad()
        output = model(X_train)
        loss = -mll(output, y_train)
        loss.backward()
        #print(f'Iter {i + 1}/{training_iter} - Loss: {loss.item():.3f}')
        optimizer.step()
    return model, likelihood

def predict_values(model, likelihood, train_len, pred_steps, embedding_vector, device):
    """使用训练好的模型进行预测，包含嵌入向量"""
    next_time_steps = torch.tensor([[train_len + 0] for i in range(pred_steps)],
                                  dtype=torch.float32).to(device)
    embedding_tensor = torch.tensor(embedding_vector, dtype=torch.float32).to(device)
    X_pred = torch.cat([next_time_steps, embedding_tensor.repeat(pred_steps, 1)], dim=1)  # [pred_steps, 33]
    
    model.eval()
    likelihood.eval()
    with torch.no_grad(), gpytorch.settings.fast_pred_var():
        next_predictions = likelihood(model(X_pred)).mean.cpu().numpy()
    return next_predictions

def cleanup_memory():
    """清理内存"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

def build_prediction_df(predictions, location, test_years, feature_cols):
    """构建预测结果 DataFrame"""
    pred_df = pd.DataFrame(predictions, columns=feature_cols)
    pred_df.insert(0, 'location', location)
    pred_df.insert(1, 'year', test_years)
    return pred_df

def safe_mape(y_true, y_pred):
    """安全地计算MAPE，避免除以零"""
    y_true, y_pred = np.array(y_true), np.array(y_pred)
    # 过滤掉真实值为0的情况
    non_zero_mask = y_true != 0
    if np.sum(non_zero_mask) == 0:
        return np.nan # 如果所有真实值都为0，无法计算MAPE
    return np.mean(np.abs((y_true[non_zero_mask] - y_pred[non_zero_mask]) / y_true[non_zero_mask])) * 100

def smape(y_true, y_pred):
    """计算sMAPE"""
    y_true, y_pred = np.array(y_true), np.array(y_pred)
    numerator = np.abs(y_pred - y_true)
    denominator = (np.abs(y_true) + np.abs(y_pred)) / 2
    # 避免分母为0
    non_zero_mask = denominator != 0
    if np.sum(non_zero_mask) == 0:
        return np.nan
    return np.mean(numerator[non_zero_mask] / denominator[non_zero_mask]) * 100

def mase(y_true, y_pred, y_train):
    """计算MASE"""
    y_true, y_pred, y_train = np.array(y_true), np.array(y_pred), np.array(y_train)
    # 计算分子：预测误差的MAE
    mae_forecast = np.mean(np.abs(y_true - y_pred))
    # 计算分母：训练集上朴素预测的MAE
    mae_naive_insample = np.mean(np.abs(y_train[1:] - y_train[:-1]))
    if mae_naive_insample == 0:
        return np.nan # 无法计算
    return mae_forecast / mae_naive_insample

def evaluate_predictions(true_df, pred_df, train_df_raw, feature_start_idx=2):
    """
    计算新的相对评估指标：R², MAPE, sMAPE, MASE
    接收 train_df_raw 用于计算MASE
    """
    true_df = true_df.set_index('year')
    pred_df = pred_df.set_index('year')
    train_df_raw = train_df_raw.set_index('year') # 确保train_df也有年份索引

    common_years = true_df.index.intersection(pred_df.index)
    feature_cols = true_df.columns[feature_start_idx:]
    
    # 初始化指标列表
    r2_list, mape_list, smape_list, mase_list = [], [], [], []

    for year in common_years:
        true_vals = true_df.loc[year, feature_cols].values
        pred_vals = pred_df.loc[year, feature_cols].values
        # MASE需要用到所有训练数据，而不是按年
        train_vals = train_df_raw[feature_cols].values

        # 计算各项指标
        r2 = r2_score(true_vals, pred_vals)
        mape_val = safe_mape(true_vals, pred_vals)
        smape_val = smape(true_vals, pred_vals)
        mase_val = mase(true_vals, pred_vals, train_vals)

        # 添加到列表
        r2_list.append(r2)
        mape_list.append(mape_val)
        smape_list.append(smape_val)
        mase_list.append(mase_val)
    
    return [r2_list, mape_list, smape_list, mase_list]

def process_single_country(df, test_size, device, embedding_df, first_layer_scaler):
    """
    处理单个国家的预测和评估。
    【修改】：接收 first_layer_scaler 参数，用于最终的逆标准化。
    """
    country = df['location'].iloc[0]
    train_years, test_years = split_train_test_years(df, test_size)
    
    # 这里的 train_df 和 test_df 都是第一次标准化后的数据
    train_df, test_df = get_train_test_data(df, train_years, test_years)

    # 对训练数据进行二次标准化，并保存好局部的 scaler (second_scaler)
    features_scaled, second_scaler = standardize_data(train_df)
    
    # 获取国家嵌入向量（32 维）
    embedding_vector = embedding_df.loc[country].values.reshape(1, -1)  # [1, 32]
    
    # 准备训练输入：时间 + 嵌入向量
    time_steps = np.arange(len(features_scaled)).reshape(-1, 1)  # [num_years, 1]
    embedding_repeated = np.repeat(embedding_vector, len(features_scaled), axis=0)  # [num_years, 32]
    X_train = np.hstack([time_steps, embedding_repeated])  # [num_years, 33]
    X_train = torch.tensor(X_train, dtype=torch.float32).to(device)
    y_train = torch.tensor(features_scaled.values, dtype=torch.float32).to(device)

    # 创建和训练模型
    model, likelihood = create_gp_model(X_train, y_train, device)
    model, likelihood = train_model(model, likelihood, X_train, y_train)

    # 预测
    pred_steps = len(test_years)
    predictions = predict_values(model, likelihood, len(train_df), pred_steps, embedding_vector, device)

    # 步骤 1: 对预测结果进行第一次逆标准化 (使用 second_scaler)
    # 将其从“二次局部尺度”还原到“一次全局尺度”
    predictions_first_scale = second_scaler.inverse_transform(predictions)
    
    # 步骤 2: 对预测结果进行第二次逆标准化 (使用 first_layer_scaler)
    # 将其从“一次全局尺度”还原到“原始粗数据尺度”
    predictions_raw = first_layer_scaler.inverse_transform(predictions_first_scale)

    # 同时，也要对验证集进行逆标准化，还原到“原始粗数据尺度”
    test_features_raw = first_layer_scaler.inverse_transform(test_df.iloc[:, 2:])
# --- 【新增】将训练集也反标准化，为MASE做准备 ---
    train_features_raw = first_layer_scaler.inverse_transform(train_df.iloc[:, 2:])
    # 清理内存
    cleanup_memory()

    # 使用还原到粗数据尺度的结果来构建 DataFrame
    pred_df_raw = build_prediction_df(predictions_raw, country, test_years, features_scaled.columns)
    test_df_raw = build_prediction_df(test_features_raw, country, test_years, features_scaled.columns)
    
    # --- 【新增】为train_df_raw也构建DataFrame ---
    train_df_raw_full = build_prediction_df(train_features_raw, country, train_years, features_scaled.columns)
    # ----------------------------------------
    
    # --- 【修改】调用新的评估函数 ---
    metrics = evaluate_predictions(test_df_raw, pred_df_raw, train_df_raw_full)
    # --------------------------------
    return metrics

def scale_result_dict(result_dict, outlier_threshold=1.5, handle_outliers='cap'):
    """标准化 result_dict 数据"""
    result_dict_scale = {}
    # 创建一个空字典，用来存放每个国家的scaler
    scaler_dict = {} 

    for country, df in result_dict.items():
        # 为每个国家创建一个新的scaler实例
        scaler = MinMaxScaler()
        features = df.iloc[:, 2:].copy()
        
        if handle_outliers in ['cap', 'remove']:
            for col in features.columns:
                Q1 = features[col].quantile(0.25)
                Q3 = features[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - outlier_threshold * IQR
                upper_bound = Q3 + outlier_threshold * IQR

                outliers = (features[col] < lower_bound) | (features[col] > upper_bound)
                if outliers.any():
                    print(f"Country {country}, Column {col}: Detected {outliers.sum()} outliers")
                
                if handle_outliers == 'cap':
                    features.loc[features[col] < lower_bound, col] = lower_bound
                    features.loc[features[col] > upper_bound, col] = upper_bound
                elif handle_outliers == 'remove':
                    features = features[~outliers]
                    df = df.loc[features.index]

        # 使用 scaler 对粗数据进行第一次标准化
        scaled_features = scaler.fit_transform(features)
        
        # 将这个为特定国家学习到的 scaler 保存到字典中
        scaler_dict[country] = scaler
        scaled_df = pd.DataFrame(scaled_features, columns=features.columns)
        scaled_df.insert(0, 'location', df['location'].values)
        scaled_df.insert(1, 'year', df['year'].values)
        result_dict_scale[country] = scaled_df
    
    return result_dict_scale, scaler_dict

# 在主流程循环之前，获取测试年份
# 假设所有国家的时间范围一致，我们用第一个国家来确定测试年份
test_size = -5 # 首先定义 test_size
any_country_df = result_dict[next(iter(result_dict))] 
_, test_years = split_train_test_years(any_country_df, test_size)


# ==============================================================================
# 主流程 (最终执行版)
# ==============================================================================

# 假设原始的 result_dict 已经加载

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
metric_dict = {}
test_size = -5

# 步骤 1：进行第一次全局标准化，并获取所有国家的 scaler
result_dict_scale, scaler_dict_global = scale_result_dict(result_dict, outlier_threshold=10, handle_outliers='cap')

# 步骤 2：加载GCN生成的国家嵌入数据
cor_space_country = pd.read_csv("所有国家空间协变量emb.csv", index_col=[0])

# --- 【请在这里添加】 ---
countries_to_process = list(result_dict_scale.keys())

# 步骤 3：遍历所有国家，执行预测和评估
# tqdm会自动创建进度条，显示进度、耗时和ETA
# 【修正后的版本】

# 步骤 3：遍历所有国家，执行预测和评估
for country in tqdm(countries_to_process, desc="国家总进度"):
    df_target = result_dict_scale[country]
    first_layer_scaler = scaler_dict_global[country]
    
    # 执行完整的单国处理流程
    metrics = process_single_country(df_target, test_size, device, cor_space_country, first_layer_scaler)
    
    # 将计算出的新指标列表存入字典
    metric_dict[country] = metrics
    
    # 【注意】：循环末尾不再有任何打印语句，保持简洁！

#输出结果

import pandas as pd
import numpy as np

# 假设 metric_dict 已经从上一个cell正确生成
# 并且 test_years 也已经定义

# 1. 初始数据转换
df = pd.DataFrame.from_dict(metric_dict).T.copy()
# 新的列名
df.columns = ['R2_list', 'MAPE_list', 'sMAPE_list', 'MASE_list']
metrics_names = ['R2', 'MAPE', 'sMAPE', 'MASE']

# --- 创建一个包含所有年度和平均指标的、更详细的DataFrame ---
all_results = []
for country in df.index:
    for i, metric_name in enumerate(metrics_names):
        yearly_values = df.loc[country, f'{metric_name}_list']
        # 计算5年平均值
        mean_val = np.nanmean(yearly_values) # 使用nanmean忽略可能的nan值
        
        # 记录每年的值和5年平均值
        row_data = {'Country': country, 'Metric': metric_name, '5yr_Avg': mean_val}
        for j, year in enumerate(test_years):
            row_data[f'Year_{year}'] = yearly_values[j]
        all_results.append(row_data)

long_format_df = pd.DataFrame(all_results)
print("--- Detailed Results by Country and Metric (Long Format) ---")
print(long_format_df.head(8)) # 打印前两个国家的结果看看


# --- 2. 计算并展示【年度】全球描述性统计 ---
yearly_stats_list = []
for year in test_years:
    year_col = f'Year_{year}'
    # 按指标分组，计算该年份的统计数据
    stats = long_format_df.groupby('Metric')[year_col].agg(['median', 'std', lambda x: x.quantile(0.25), lambda x: x.quantile(0.75)]).reset_index()
    stats.columns = ['Metric', 'Median', 'Std_Dev', '25th_Percentile', '75th_Percentile']
    stats.insert(0, 'Year', year)
    yearly_stats_list.append(stats)

yearly_global_stats_df = pd.concat(yearly_stats_list, ignore_index=True)
print("\n\n--- Yearly Global Descriptive Statistics ---")
print(yearly_global_stats_df)


# --- 3. 计算并展示【5年汇总】全球描述性统计 ---
agg_stats = long_format_df.groupby('Metric')['5yr_Avg'].agg(['median', 'std', lambda x: x.quantile(0.25), lambda x: x.quantile(0.75)]).reset_index()
agg_stats.columns = ['Metric', 'Median', 'Std_Dev', '25th_Percentile', '75th_Percentile']

print("\n\n--- 5-Year Aggregated Global Descriptive Statistics ---")
print(agg_stats)


# --- 4. 保存结果到CSV ---
# 保存每个国家的详细结果，用于后续分析
long_format_df.to_csv("prediction_metrics_detailed_by_country.csv", index=False)

# 保存两个全局统计结果
yearly_global_stats_df.to_csv("prediction_metrics_yearly_global_stats.csv", index=False)
agg_stats.to_csv("prediction_metrics_5yr_agg_global_stats.csv", index=False)

print("\nAll detailed and summary results have been saved to CSV files.")