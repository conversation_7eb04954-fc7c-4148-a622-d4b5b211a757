#第一段代码：预测结果评估
from model_script import *
from utilts import *
from tqdm import tqdm 
import pandas as pd
import numpy as np
import torch
import gpytorch
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import MinMaxScaler
import gc

filled_df=pd.read_csv("筛选后协变量特征值列表.csv")
print(filled_df.shape)
# 按location分组
grouped = filled_df.groupby('location')
# 创建字典，key是location，value是对应的分组数据框
result_dict = {location: group for location, group in grouped}
# 打印结果
for location, group in result_dict.items():
    print(f"Location: {location}")
    print(group)
    break
cor_space_country = pd.read_csv("所有国家空间协变量emb.csv", index_col=0)

def split_train_test_years(df, test_size=-5):
    """划分训练和测试年份"""
    years = df['year'].sort_values().unique()
    if abs(test_size) >= len(years):
        raise ValueError("Test size must be smaller than the total number of years")
    test_years = years[test_size:].tolist()
    train_years = years[:test_size].tolist()
    return train_years, test_years

def get_train_test_data(df, train_years, test_years):
    """根据年份划分训练和测试数据集"""
    train_df = df[df['year'].isin(train_years)]
    test_df = df[df['year'].isin(test_years)]
    return train_df, test_df

def standardize_data(df, feature_start_idx=2):
    """标准化特征数据"""
    scaler = MinMaxScaler()
    features_scaled = scaler.fit_transform(df.iloc[:, feature_start_idx:])
    return pd.DataFrame(features_scaled, columns=df.iloc[:, feature_start_idx:].columns), scaler

def create_gp_model(X_train, y_train, device):
    """创建并返回高斯过程模型和似然函数，适应时间+空间嵌入输入"""
    class MultitaskGPModel(gpytorch.models.ExactGP):
        def __init__(self, train_x, train_y, likelihood):
            super(MultitaskGPModel, self).__init__(train_x, train_y, likelihood)
            self.mean_module = gpytorch.means.MultitaskMean(
                gpytorch.means.ConstantMean(), num_tasks=y_train.shape[1]
            )
            self.covar_module = gpytorch.kernels.MultitaskKernel(
                gpytorch.kernels.ScaleKernel(
                    gpytorch.kernels.ProductKernel(
                        gpytorch.kernels.RBFKernel(active_dims=[0]),  # 时间维度
                        gpytorch.kernels.RBFKernel(active_dims=range(1, 33))  # 嵌入维度 (32 维)
                    )
                ),
                num_tasks=y_train.shape[1], rank=1
            )

        def forward(self, x):
            mean_x = self.mean_module(x)
            covar_x = self.covar_module(x)
            return gpytorch.distributions.MultitaskMultivariateNormal(mean_x, covar_x)

    likelihood = gpytorch.likelihoods.MultitaskGaussianLikelihood(num_tasks=y_train.shape[1]).to(device)
    model = MultitaskGPModel(X_train, y_train, likelihood).to(device)
    return model, likelihood

def train_model(model, likelihood, X_train, y_train, training_iter=80, lr=0.1):
    """训练高斯过程模型"""
    model.train()
    likelihood.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    mll = gpytorch.mlls.ExactMarginalLogLikelihood(likelihood, model)

    # 【修改这里】为训练迭代也加上tqdm
    # leave=False 表示这个内部进度条完成后会消失，保持界面干净
    for i in tqdm(range(training_iter), desc="模型训练中", leave=False):
        optimizer.zero_grad()
        output = model(X_train)
        loss = -mll(output, y_train)
        loss.backward()
        #print(f'Iter {i + 1}/{training_iter} - Loss: {loss.item():.3f}')
        optimizer.step()
    return model, likelihood

def predict_values(model, likelihood, train_len, pred_steps, embedding_vector, device):
    """使用训练好的模型进行预测，包含嵌入向量"""
    next_time_steps = torch.tensor([[train_len + 0] for i in range(pred_steps)],
                                  dtype=torch.float32).to(device)
    embedding_tensor = torch.tensor(embedding_vector, dtype=torch.float32).to(device)
    X_pred = torch.cat([next_time_steps, embedding_tensor.repeat(pred_steps, 1)], dim=1)  # [pred_steps, 33]
    
    model.eval()
    likelihood.eval()
    with torch.no_grad(), gpytorch.settings.fast_pred_var():
        next_predictions = likelihood(model(X_pred)).mean.cpu().numpy()
    return next_predictions

def cleanup_memory():
    """清理内存"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

def build_prediction_df(predictions, location, test_years, feature_cols):
    """构建预测结果 DataFrame"""
    pred_df = pd.DataFrame(predictions, columns=feature_cols)
    pred_df.insert(0, 'location', location)
    pred_df.insert(1, 'year', test_years)
    return pred_df

def safe_mape(y_true, y_pred):
    """安全地计算MAPE，避免除以零"""
    y_true, y_pred = np.array(y_true), np.array(y_pred)
    # 过滤掉真实值为0的情况
    non_zero_mask = y_true != 0
    if np.sum(non_zero_mask) == 0:
        return np.nan # 如果所有真实值都为0，无法计算MAPE
    return np.mean(np.abs((y_true[non_zero_mask] - y_pred[non_zero_mask]) / y_true[non_zero_mask])) * 100

def smape(y_true, y_pred):
    """计算sMAPE"""
    y_true, y_pred = np.array(y_true), np.array(y_pred)
    numerator = np.abs(y_pred - y_true)
    denominator = (np.abs(y_true) + np.abs(y_pred)) / 2
    # 避免分母为0
    non_zero_mask = denominator != 0
    if np.sum(non_zero_mask) == 0:
        return np.nan
    return np.mean(numerator[non_zero_mask] / denominator[non_zero_mask]) * 100

def mase(y_true, y_pred, y_train):
    """计算MASE"""
    y_true, y_pred, y_train = np.array(y_true), np.array(y_pred), np.array(y_train)
    # 计算分子：预测误差的MAE
    mae_forecast = np.mean(np.abs(y_true - y_pred))
    # 计算分母：训练集上朴素预测的MAE
    mae_naive_insample = np.mean(np.abs(y_train[1:] - y_train[:-1]))
    if mae_naive_insample == 0:
        return np.nan # 无法计算
    return mae_forecast / mae_naive_insample

def evaluate_predictions(true_df, pred_df, train_df_raw, feature_start_idx=2):
    """
    计算新的相对评估指标：R², MAPE, sMAPE, MASE
    接收 train_df_raw 用于计算MASE
    """
    true_df = true_df.set_index('year')
    pred_df = pred_df.set_index('year')
    train_df_raw = train_df_raw.set_index('year') # 确保train_df也有年份索引

    common_years = true_df.index.intersection(pred_df.index)
    feature_cols = true_df.columns[feature_start_idx:]
    
    # 初始化指标列表
    r2_list, mape_list, smape_list, mase_list = [], [], [], []

    for year in common_years:
        true_vals = true_df.loc[year, feature_cols].values
        pred_vals = pred_df.loc[year, feature_cols].values
        # MASE需要用到所有训练数据，而不是按年
        train_vals = train_df_raw[feature_cols].values

        # 计算各项指标
        r2 = r2_score(true_vals, pred_vals)
        mape_val = safe_mape(true_vals, pred_vals)
        smape_val = smape(true_vals, pred_vals)
        mase_val = mase(true_vals, pred_vals, train_vals)

        # 添加到列表
        r2_list.append(r2)
        mape_list.append(mape_val)
        smape_list.append(smape_val)
        mase_list.append(mase_val)
    
    return [r2_list, mape_list, smape_list, mase_list]

def process_single_country(df, test_size, device, embedding_df, first_layer_scaler):
    """
    处理单个国家的预测和评估。
    【修改】：接收 first_layer_scaler 参数，用于最终的逆标准化。
    """
    country = df['location'].iloc[0]
    train_years, test_years = split_train_test_years(df, test_size)
    
    # 这里的 train_df 和 test_df 都是第一次标准化后的数据
    train_df, test_df = get_train_test_data(df, train_years, test_years)

    # 对训练数据进行二次标准化，并保存好局部的 scaler (second_scaler)
    features_scaled, second_scaler = standardize_data(train_df)
    
    # 获取国家嵌入向量（32 维）
    embedding_vector = embedding_df.loc[country].values.reshape(1, -1)  # [1, 32]
    
    # 准备训练输入：时间 + 嵌入向量
    time_steps = np.arange(len(features_scaled)).reshape(-1, 1)  # [num_years, 1]
    embedding_repeated = np.repeat(embedding_vector, len(features_scaled), axis=0)  # [num_years, 32]
    X_train = np.hstack([time_steps, embedding_repeated])  # [num_years, 33]
    X_train = torch.tensor(X_train, dtype=torch.float32).to(device)
    y_train = torch.tensor(features_scaled.values, dtype=torch.float32).to(device)

    # 创建和训练模型
    model, likelihood = create_gp_model(X_train, y_train, device)
    model, likelihood = train_model(model, likelihood, X_train, y_train)

    # 预测
    pred_steps = len(test_years)
    predictions = predict_values(model, likelihood, len(train_df), pred_steps, embedding_vector, device)

    # 步骤 1: 对预测结果进行第一次逆标准化 (使用 second_scaler)
    # 将其从“二次局部尺度”还原到“一次全局尺度”
    predictions_first_scale = second_scaler.inverse_transform(predictions)
    
    # 步骤 2: 对预测结果进行第二次逆标准化 (使用 first_layer_scaler)
    # 将其从“一次全局尺度”还原到“原始粗数据尺度”
    predictions_raw = first_layer_scaler.inverse_transform(predictions_first_scale)

    # 同时，也要对验证集进行逆标准化，还原到“原始粗数据尺度”
    test_features_raw = first_layer_scaler.inverse_transform(test_df.iloc[:, 2:])
# --- 【新增】将训练集也反标准化，为MASE做准备 ---
    train_features_raw = first_layer_scaler.inverse_transform(train_df.iloc[:, 2:])
    # 清理内存
    cleanup_memory()

    # 使用还原到粗数据尺度的结果来构建 DataFrame
    pred_df_raw = build_prediction_df(predictions_raw, country, test_years, features_scaled.columns)
    test_df_raw = build_prediction_df(test_features_raw, country, test_years, features_scaled.columns)
    
    # --- 【新增】为train_df_raw也构建DataFrame ---
    train_df_raw_full = build_prediction_df(train_features_raw, country, train_years, features_scaled.columns)
    # ----------------------------------------
    
    # --- 【修改】调用新的评估函数 ---
    metrics = evaluate_predictions(test_df_raw, pred_df_raw, train_df_raw_full)
    # --------------------------------
    return metrics

def scale_result_dict(result_dict, outlier_threshold=1.5, handle_outliers='cap'):
    """标准化 result_dict 数据"""
    result_dict_scale = {}
    # 创建一个空字典，用来存放每个国家的scaler
    scaler_dict = {} 

    for country, df in result_dict.items():
        # 为每个国家创建一个新的scaler实例
        scaler = MinMaxScaler()
        features = df.iloc[:, 2:].copy()
        
        if handle_outliers in ['cap', 'remove']:
            for col in features.columns:
                Q1 = features[col].quantile(0.25)
                Q3 = features[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - outlier_threshold * IQR
                upper_bound = Q3 + outlier_threshold * IQR

                outliers = (features[col] < lower_bound) | (features[col] > upper_bound)
                if outliers.any():
                    print(f"Country {country}, Column {col}: Detected {outliers.sum()} outliers")
                
                if handle_outliers == 'cap':
                    features.loc[features[col] < lower_bound, col] = lower_bound
                    features.loc[features[col] > upper_bound, col] = upper_bound
                elif handle_outliers == 'remove':
                    features = features[~outliers]
                    df = df.loc[features.index]

        # 使用 scaler 对粗数据进行第一次标准化
        scaled_features = scaler.fit_transform(features)
        
        # 将这个为特定国家学习到的 scaler 保存到字典中
        scaler_dict[country] = scaler
        scaled_df = pd.DataFrame(scaled_features, columns=features.columns)
        scaled_df.insert(0, 'location', df['location'].values)
        scaled_df.insert(1, 'year', df['year'].values)
        result_dict_scale[country] = scaled_df
    
    return result_dict_scale, scaler_dict

# 在主流程循环之前，获取测试年份
# 假设所有国家的时间范围一致，我们用第一个国家来确定测试年份
test_size = -5 # 首先定义 test_size
any_country_df = result_dict[next(iter(result_dict))] 
_, test_years = split_train_test_years(any_country_df, test_size)


# ==============================================================================
# 主流程 (最终执行版)
# ==============================================================================

# 假设原始的 result_dict 已经加载

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
metric_dict = {}
test_size = -5

# 步骤 1：进行第一次全局标准化，并获取所有国家的 scaler
result_dict_scale, scaler_dict_global = scale_result_dict(result_dict, outlier_threshold=10, handle_outliers='cap')

# 步骤 2：加载GCN生成的国家嵌入数据
cor_space_country = pd.read_csv("所有国家空间协变量emb.csv", index_col=[0])

# --- 【请在这里添加】 ---
countries_to_process = list(result_dict_scale.keys())

# 步骤 3：遍历所有国家，执行预测和评估
# tqdm会自动创建进度条，显示进度、耗时和ETA
# 【修正后的版本】

# 步骤 3：遍历所有国家，执行预测和评估
for country in tqdm(countries_to_process, desc="国家总进度"):
    df_target = result_dict_scale[country]
    first_layer_scaler = scaler_dict_global[country]
    
    # 执行完整的单国处理流程
    metrics = process_single_country(df_target, test_size, device, cor_space_country, first_layer_scaler)
    
    # 将计算出的新指标列表存入字典
    metric_dict[country] = metrics
    
    # 【注意】：循环末尾不再有任何打印语句，保持简洁！












#第二段代码预测结果保存
import pandas as pd
import numpy as np

# 假设 metric_dict 已经从上一个cell正确生成
# 并且 test_years 也已经定义

# 1. 初始数据转换
df = pd.DataFrame.from_dict(metric_dict).T.copy()
# 新的列名
df.columns = ['R2_list', 'MAPE_list', 'sMAPE_list', 'MASE_list']
metrics_names = ['R2', 'MAPE', 'sMAPE', 'MASE']

# --- 创建一个包含所有年度和平均指标的、更详细的DataFrame ---
all_results = []
for country in df.index:
    for i, metric_name in enumerate(metrics_names):
        yearly_values = df.loc[country, f'{metric_name}_list']
        # 计算5年平均值
        mean_val = np.nanmean(yearly_values) # 使用nanmean忽略可能的nan值
        
        # 记录每年的值和5年平均值
        row_data = {'Country': country, 'Metric': metric_name, '5yr_Avg': mean_val}
        for j, year in enumerate(test_years):
            row_data[f'Year_{year}'] = yearly_values[j]
        all_results.append(row_data)

long_format_df = pd.DataFrame(all_results)
print("--- Detailed Results by Country and Metric (Long Format) ---")
print(long_format_df.head(8)) # 打印前两个国家的结果看看


# --- 2. 计算并展示【年度】全球描述性统计 ---
yearly_stats_list = []
for year in test_years:
    year_col = f'Year_{year}'
    # 按指标分组，计算该年份的统计数据
    stats = long_format_df.groupby('Metric')[year_col].agg(['median', 'std', lambda x: x.quantile(0.25), lambda x: x.quantile(0.75)]).reset_index()
    stats.columns = ['Metric', 'Median', 'Std_Dev', '25th_Percentile', '75th_Percentile']
    stats.insert(0, 'Year', year)
    yearly_stats_list.append(stats)

yearly_global_stats_df = pd.concat(yearly_stats_list, ignore_index=True)
print("\n\n--- Yearly Global Descriptive Statistics ---")
print(yearly_global_stats_df)


# --- 3. 计算并展示【5年汇总】全球描述性统计 ---
agg_stats = long_format_df.groupby('Metric')['5yr_Avg'].agg(['median', 'std', lambda x: x.quantile(0.25), lambda x: x.quantile(0.75)]).reset_index()
agg_stats.columns = ['Metric', 'Median', 'Std_Dev', '25th_Percentile', '75th_Percentile']

print("\n\n--- 5-Year Aggregated Global Descriptive Statistics ---")
print(agg_stats)


# --- 4. 保存结果到CSV ---
# 保存每个国家的详细结果，用于后续分析
long_format_df.to_csv("prediction_metrics_detailed_by_country.csv", index=False)

# 保存两个全局统计结果
yearly_global_stats_df.to_csv("prediction_metrics_yearly_global_stats.csv", index=False)
agg_stats.to_csv("prediction_metrics_5yr_agg_global_stats.csv", index=False)

print("\nAll detailed and summary results have been saved to CSV files.")











#第三段代码：全面预测未来结果
import pandas as pd
import numpy as np
import torch
import gpytorch
from sklearn.preprocessing import MinMaxScaler
from tqdm import tqdm
import gc
def create_gp_model(X_train, y_train, device):
    """创建并返回高斯过程模型和似然函数，适应时间+空间嵌入输入"""
    class MultitaskGPModel(gpytorch.models.ExactGP):
        def __init__(self, train_x, train_y, likelihood):
            super(MultitaskGPModel, self).__init__(train_x, train_y, likelihood)
            self.mean_module = gpytorch.means.MultitaskMean(
                gpytorch.means.ConstantMean(), num_tasks=y_train.shape[1]
            )
            self.covar_module = gpytorch.kernels.MultitaskKernel(
                gpytorch.kernels.ScaleKernel(
                    gpytorch.kernels.ProductKernel(
                        gpytorch.kernels.RBFKernel(active_dims=[0]),  # 时间维度
                        gpytorch.kernels.RBFKernel(active_dims=range(1, 33))  # 嵌入维度 (32 维)
                    )
                ),
                num_tasks=y_train.shape[1], rank=1
            )

        def forward(self, x):
            mean_x = self.mean_module(x)
            covar_x = self.covar_module(x)
            return gpytorch.distributions.MultitaskMultivariateNormal(mean_x, covar_x)

    likelihood = gpytorch.likelihoods.MultitaskGaussianLikelihood(num_tasks=y_train.shape[1]).to(device)
    model = MultitaskGPModel(X_train, y_train, likelihood).to(device)
    return model, likelihood

def train_model(model, likelihood, X_train, y_train, training_iter=80, lr=0.1):
    """训练高斯过程模型"""
    model.train()
    likelihood.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    mll = gpytorch.mlls.ExactMarginalLogLikelihood(likelihood, model)

    # 【修改这里】为训练迭代也加上tqdm
    # leave=False 表示这个内部进度条完成后会消失，保持界面干净
    for i in tqdm(range(training_iter), desc="模型训练中", leave=False):
        optimizer.zero_grad()
        output = model(X_train)
        loss = -mll(output, y_train)
        loss.backward()
        #print(f'Iter {i + 1}/{training_iter} - Loss: {loss.item():.3f}')
        optimizer.step()
    return model, likelihood

def scale_all_countries_data(result_dict, outlier_threshold=10.0, handle_outliers='cap'):
    """
    对所有国家的完整历史数据进行标准化处理。
    为每个国家创建一个scaler，并返回标准化后的数据字典和scaler字典。
    """
    result_dict_scale = {}
    scaler_dict = {} 

    for country, df in result_dict.items():
        scaler = MinMaxScaler()
        features = df.iloc[:, 2:].copy()
        
        if handle_outliers in ['cap', 'remove']:
            for col in features.columns:
                Q1 = features[col].quantile(0.25)
                Q3 = features[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - outlier_threshold * IQR
                upper_bound = Q3 + outlier_threshold * IQR
                features.loc[features[col] < lower_bound, col] = lower_bound
                features.loc[features[col] > upper_bound, col] = upper_bound

        scaled_features = scaler.fit_transform(features)
        scaler_dict[country] = scaler
        
        scaled_df = pd.DataFrame(scaled_features, columns=features.columns)
        scaled_df.insert(0, 'location', df['location'].values)
        scaled_df.insert(1, 'year', df['year'].values)
        result_dict_scale[country] = scaled_df
    
    return result_dict_scale, scaler_dict

def cleanup_memory():
    """清理内存"""
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

def build_prediction_df(predictions, location, forecast_years, feature_cols):
    """构建预测结果 DataFrame"""
    pred_df = pd.DataFrame(predictions, columns=feature_cols)
    pred_df.insert(0, 'location', location)
    pred_df.insert(1, 'year', forecast_years)
    return pred_df

# ------------------------------------------------------------------------------
# 新增的核心预测函数
# ------------------------------------------------------------------------------

def predict_future_values(model, likelihood, train_len, pred_steps, embedding_vector, device):
    """使用训练好的模型进行未来值的预测"""
    # 创建未来的时间步
    future_time_steps = torch.arange(train_len, train_len + pred_steps, dtype=torch.float32).view(-1, 1).to(device)
    
    # 准备嵌入向量
    embedding_tensor = torch.tensor(embedding_vector, dtype=torch.float32).to(device)
    embedding_repeated = embedding_tensor.repeat(pred_steps, 1)
    
    # 组合成预测输入
    X_pred = torch.cat([future_time_steps, embedding_repeated], dim=1)
    
    model.eval()
    likelihood.eval()
    with torch.no_grad(), gpytorch.settings.fast_pred_var():
        # 使用模型进行预测，并获取均值
        predictions = likelihood(model(X_pred)).mean.cpu().numpy()
    return predictions

def process_country_forecast(df_scaled, country_scaler, device, embedding_df, forecast_years):
    """
    处理单个国家的完整预测流程：训练 -> 预测 -> 还原尺度
    
    Args:
        df_scaled (pd.DataFrame): 单个国家的、已经标准化后的历史数据 (1960-2024).
        country_scaler (MinMaxScaler): 用于该国数据的scaler对象.
        device (torch.device): 'cuda' or 'cpu'.
        embedding_df (pd.DataFrame): 包含所有国家空间嵌入的DataFrame.
        forecast_years (list): 需要预测的年份列表, e.g., [2025, 2026, ..., 2100].

    Returns:
        pd.DataFrame: 包含该国未来预测值的DataFrame (原始数据尺度).
    """
    country = df_scaled['location'].iloc[0]
    
    # 1. 准备训练数据 (使用全部历史数据)
    features_scaled = df_scaled.iloc[:, 2:]
    
    # 获取国家嵌入向量 (32 维)
    embedding_vector = embedding_df.loc[country].values.reshape(1, -1)  # [1, 32]
    
    # 准备训练输入 X_train: 时间 + 嵌入向量
    time_steps = np.arange(len(features_scaled)).reshape(-1, 1)  # [num_years, 1]
    embedding_repeated = np.repeat(embedding_vector, len(features_scaled), axis=0)  # [num_years, 32]
    X_train = np.hstack([time_steps, embedding_repeated])  # [num_years, 33]
    X_train = torch.tensor(X_train, dtype=torch.float32).to(device)
    
    # 准备训练输出 y_train
    y_train = torch.tensor(features_scaled.values, dtype=torch.float32).to(device)

    # 2. 创建和训练模型
    model, likelihood = create_gp_model(X_train, y_train, device)
    model, likelihood = train_model(model, likelihood, X_train, y_train)

    # 3. 预测未来值
    train_len = len(df_scaled)
    pred_steps = len(forecast_years)
    predictions_scaled = predict_future_values(model, likelihood, train_len, pred_steps, embedding_vector, device)

    # 4. 将预测结果还原到原始数据尺度
    predictions_raw = country_scaler.inverse_transform(predictions_scaled)

    # 5. 清理内存
    cleanup_memory()

    # 6. 构建并返回结果DataFrame
    pred_df_raw = build_prediction_df(predictions_raw, country, forecast_years, features_scaled.columns)
    
    return pred_df_raw

# ==============================================================================
# 主流程 (执行预测任务)
# ==============================================================================

if __name__ == '__main__':
    # 1. 加载数据
    print("正在加载数据...")
    try:
        filled_df = pd.read_csv("筛选后协变量特征值列表.csv")
        cor_space_country = pd.read_csv("所有国家空间协变量emb.csv", index_col=0)
    except FileNotFoundError as e:
        print(f"错误: 无法找到文件 {e.filename}。请确保文件与脚本在同一目录下。")
        exit()
        
    print(f"原始数据加载完成，形状为: {filled_df.shape}")
    print(f"空间嵌入数据加载完成，形状为: {cor_space_country.shape}")

    # 2. 按国家分组
    grouped = filled_df.groupby('location')
    result_dict = {location: group.reset_index(drop=True) for location, group in grouped}

    # 3. 设置参数
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"将使用设备: {device}")
    
    # 定义预测年份范围
    forecast_start_year = 2025
    forecast_end_year = 2100
    forecast_years = list(range(forecast_start_year, forecast_end_year + 1))
    print(f"预测范围: {forecast_start_year} 年到 {forecast_end_year} 年，共 {len(forecast_years)} 年。")

    # 4. 对所有国家的历史数据进行标准化，并获取各自的scaler
    print("正在对所有国家的历史数据进行标准化...")
    result_dict_scaled, scaler_dict = scale_all_countries_data(result_dict)
    print("数据标准化完成。")

    # 5. 遍历所有国家，执行预测
    all_predictions_list = []
    countries_to_process = list(result_dict_scaled.keys())

    # 使用tqdm创建主进度条
    for country in tqdm(countries_to_process, desc="国家总预测进度"):
        df_scaled_country = result_dict_scaled[country]
        country_scaler = scaler_dict[country]
        
        # 执行单个国家的完整预测流程
        country_prediction_df = process_country_forecast(
            df_scaled=df_scaled_country,
            country_scaler=country_scaler,
            device=device,
            embedding_df=cor_space_country,
            forecast_years=forecast_years
        )
        
        all_predictions_list.append(country_prediction_df)

    # 6. 合并所有国家的预测结果并保存
    print("所有国家预测完成，正在合并结果...")
    final_predictions_df = pd.concat(all_predictions_list, ignore_index=True)

    # 7. 保存到CSV文件
    output_filename = "未来协变量预测结果(2025-2100).csv"
    final_predictions_df.to_csv(output_filename, index=False)

    print("-" * 50)
    print("预测流程全部完成！")
    print(f"最终预测结果的形状: {final_predictions_df.shape}")
    print(f"结果已保存至: {output_filename}")
    print("\n部分预测结果预览:")
    print(final_predictions_df.head())
