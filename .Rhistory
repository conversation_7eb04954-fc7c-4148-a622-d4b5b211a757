# type="density" 表示在边缘绘制密度曲线
# groupFill=TRUE 表示让密度曲线的填充颜色与点的颜色保持一致
p2 <- ggMarginal(p, type = "density", groupColour = TRUE, groupFill = TRUE, alpha=0.3)
# 显示最终的图片
print(p2)
# --- 步骤 6: 保存图片 (可选) ---
# 如果需要将图片保存为PDF文件，可以取消下面这行的注释
ggsave("Country_Clusters_Visualization.pdf", plot = p2, width = 25, height = 25)
library(tidyverse) # 包含 ggplot2，用于绘图
library(ggrepel)   # 用于防止标签重叠
library(RColorBrewer) # 提供美观的调色板
library(ggExtra)   # 用于绘制边缘密度图
# --- 步骤 1.5: 设置工作目录 (可选) ---
# 如果您的CSV文件不在R的默认工作目录下，请取消下面这行的注释并修改为您自己的路径
setwd("F:\\博士课题\\part1-协变量部分")
# --- 步骤 2: 读取你的数据 ---
# 请确保 "country_clusters.csv" 文件与你的 R 脚本在同一个工作目录下
df <- read.csv("country_clusters.csv")
# --- 步骤 3: 数据预处理 ---
# 将 Cluster 列转换为因子（factor）类型，这样 ggplot 才会把它当作分组变量来分配颜色
# 我们也过滤掉噪声点（标签为-1），因为它们不属于任何一个簇
df <- df %>%
filter(Cluster != -1) %>%
mutate(Cluster = as.factor(Cluster))
# --- 【修改】步骤 3.5: 定义手动颜色方案 ---
# 创建一个包含5种颜色的向量，为5个簇手动指定颜色
# 您可以根据喜好修改这些十六进制颜色代码
my_colors <- c("0" = "#E41A1C", "1" = "#377EB8", "2" = "#4DAF4A", "3" = "#984EA3", "4" = "#FF7F00")
# --- 步骤 4: 绘制核心图形 ---
p <- ggplot(df, aes(x = TSNE_1, y = TSNE_2, color = Cluster, label = Country)) +
# 使用 geom_jitter 代替 geom_point，为重叠的点增加微小的随机扰动（弹性）
geom_jitter(width = 0.2, height = 0.2, size = 3, alpha = 0.8) +
# 在 geom_text_repel 中增加参数来增强“弹性”并去掉连线
geom_text_repel(
color = "black",
size = 3.5,
max.overlaps = 20,      # 允许更多的标签重叠（实际上是让算法更努力地去放置它们）
force = 1.5,            # 增强标签之间的排斥力
segment.color = NA      # 将连线颜色设为NA，使其完全不可见
) +
# 【修改】: 使用 scale_color_manual 手动设置颜色
scale_color_manual(values = my_colors) +
# 将 alpha 设置为 0，使圈内背景完全透明
stat_ellipse(aes(color = Cluster), geom = "polygon", level = 0.95, alpha = 0) +
# 【修改】: 设置坐标轴和新的英文标题
labs(x = "t-SNE Component 1", y = "t-SNE Component 2", title = "t-SNE Visualization of Country Clusters (DBSCAN)") +
# 使用经典主题，并进行美化
theme_bw() +
theme(
axis.line = element_line(colour = "black"),
axis.title = element_text(color = "black", size = 14, face = "bold"),
axis.text = element_text(color = "black", size = 12),
plot.title = element_text(hjust = 0.5, size = 20, face = "bold"),
legend.position = "none" # 隐藏图例，让画面更干净
)
# --- 步骤 5: 添加边缘密度图并显示最终图片 ---
# type="density" 表示在边缘绘制密度曲线
# groupFill=TRUE 表示让密度曲线的填充颜色与点的颜色保持一致
p2 <- ggMarginal(p, type = "density", groupColour = TRUE, groupFill = TRUE, alpha=0.3)
# 显示最终的图片
print(p2)
# --- 步骤 6: 保存图片 (可选) ---
# 如果需要将图片保存为PDF文件，可以取消下面这行的注释
ggsave("Country_Clusters_Visualization.pdf", plot = p2, width = 25, height = 20)
library(tidyverse) # 包含 ggplot2，用于绘图
library(ggrepel)   # 用于防止标签重叠
library(RColorBrewer) # 提供美观的调色板
library(ggExtra)   # 用于绘制边缘密度图
# --- 步骤 1.5: 设置工作目录 (可选) ---
# 如果您的CSV文件不在R的默认工作目录下，请取消下面这行的注释并修改为您自己的路径
setwd("F:\\博士课题\\part1-协变量部分")
# --- 步骤 2: 读取你的数据 ---
# 请确保 "country_clusters.csv" 文件与你的 R 脚本在同一个工作目录下
df <- read.csv("country_clusters.csv")
# --- 步骤 3: 数据预处理 ---
# 将 Cluster 列转换为因子（factor）类型，这样 ggplot 才会把它当作分组变量来分配颜色
# 我们也过滤掉噪声点（标签为-1），因为它们不属于任何一个簇
df <- df %>%
filter(Cluster != -1) %>%
mutate(Cluster = as.factor(Cluster))
# --- 【修改】步骤 3.5: 定义手动颜色方案 ---
# 创建一个包含5种颜色的向量，为5个簇手动指定颜色
# 您可以根据喜好修改这些十六进制颜色代码
my_colors <- c("0" = "#E41A1C", "1" = "#377EB8", "2" = "#4DAF4A", "3" = "#984EA3", "4" = "#FF7F00")
# --- 步骤 4: 绘制核心图形 ---
p <- ggplot(df, aes(x = TSNE_1, y = TSNE_2, color = Cluster, label = Country)) +
# 使用 geom_jitter 代替 geom_point，为重叠的点增加微小的随机扰动（弹性）
geom_jitter(width = 0.2, height = 0.2, size = 3, alpha = 0.8) +
# 在 geom_text_repel 中增加参数来增强“弹性”并去掉连线
geom_text_repel(
color = "black",
size =4,
max.overlaps = 20,      # 允许更多的标签重叠（实际上是让算法更努力地去放置它们）
force = 1.5,            # 增强标签之间的排斥力
segment.color = NA      # 将连线颜色设为NA，使其完全不可见
) +
# 【修改】: 使用 scale_color_manual 手动设置颜色
scale_color_manual(values = my_colors) +
# 将 alpha 设置为 0，使圈内背景完全透明
stat_ellipse(aes(color = Cluster), geom = "polygon", level = 0.95, alpha = 0) +
# 【修改】: 设置坐标轴和新的英文标题
labs(x = "t-SNE Component 1", y = "t-SNE Component 2", title = "t-SNE Visualization of Country Clusters (DBSCAN)") +
# 使用经典主题，并进行美化
theme_bw() +
theme(
axis.line = element_line(colour = "black"),
axis.title = element_text(color = "black", size = 14, face = "bold"),
axis.text = element_text(color = "black", size = 12),
plot.title = element_text(hjust = 0.5, size = 20, face = "bold"),
legend.position = "none" # 隐藏图例，让画面更干净
)
# --- 步骤 5: 添加边缘密度图并显示最终图片 ---
# type="density" 表示在边缘绘制密度曲线
# groupFill=TRUE 表示让密度曲线的填充颜色与点的颜色保持一致
p2 <- ggMarginal(p, type = "density", groupColour = TRUE, groupFill = TRUE, alpha=0.3)
# 显示最终的图片
print(p2)
# --- 步骤 6: 保存图片 (可选) ---
# 如果需要将图片保存为PDF文件，可以取消下面这行的注释
ggsave("Country_Clusters_Visualization.pdf", plot = p2, width = 25, height = 20)
library(tidyverse) # 包含 ggplot2，用于绘图
library(ggrepel)   # 用于防止标签重叠
library(RColorBrewer) # 提供美观的调色板
library(ggExtra)   # 用于绘制边缘密度图
# --- 步骤 1.5: 设置工作目录 (可选) ---
# 如果您的CSV文件不在R的默认工作目录下，请取消下面这行的注释并修改为您自己的路径
setwd("F:\\博士课题\\part1-协变量部分")
# --- 步骤 2: 读取你的数据 ---
# 请确保 "country_clusters.csv" 文件与你的 R 脚本在同一个工作目录下
df <- read.csv("country_clusters.csv")
# --- 步骤 3: 数据预处理 ---
# 将 Cluster 列转换为因子（factor）类型，这样 ggplot 才会把它当作分组变量来分配颜色
# 我们也过滤掉噪声点（标签为-1），因为它们不属于任何一个簇
df <- df %>%
filter(Cluster != -1) %>%
mutate(Cluster = as.factor(Cluster))
# --- 【修改】步骤 3.5: 定义手动颜色方案 ---
# 创建一个包含5种颜色的向量，为5个簇手动指定颜色
# 您可以根据喜好修改这些十六进制颜色代码
my_colors <- c("0" = "#E51C23", "1" = "#3F51B5", "2" = "#75C500", "3" = "#9C27B0", "4" = "#FF9800")
# --- 步骤 4: 绘制核心图形 ---
p <- ggplot(df, aes(x = TSNE_1, y = TSNE_2, color = Cluster, label = Country)) +
# 使用 geom_jitter 代替 geom_point，为重叠的点增加微小的随机扰动（弹性）
geom_jitter(width = 0.2, height = 0.2, size = 3, alpha = 0.8) +
# 在 geom_text_repel 中增加参数来增强“弹性”并去掉连线
geom_text_repel(
color = "black",
size =4,
max.overlaps = 20,      # 允许更多的标签重叠（实际上是让算法更努力地去放置它们）
force = 1.5,            # 增强标签之间的排斥力
segment.color = NA      # 将连线颜色设为NA，使其完全不可见
) +
# 【修改】: 使用 scale_color_manual 手动设置颜色
scale_color_manual(values = my_colors) +
# 将 alpha 设置为 0，使圈内背景完全透明
stat_ellipse(aes(color = Cluster), geom = "polygon", level = 0.95, alpha = 0) +
# 【修改】: 设置坐标轴和新的英文标题
labs(x = "t-SNE Component 1", y = "t-SNE Component 2", title = "t-SNE Visualization of Country Clusters (DBSCAN)") +
# 使用经典主题，并进行美化
theme_bw() +
theme(
axis.line = element_line(colour = "black"),
axis.title = element_text(color = "black", size = 14, face = "bold"),
axis.text = element_text(color = "black", size = 12),
plot.title = element_text(hjust = 0.5, size = 20, face = "bold"),
legend.position = "none" # 隐藏图例，让画面更干净
)
# --- 步骤 5: 添加边缘密度图并显示最终图片 ---
# type="density" 表示在边缘绘制密度曲线
# groupFill=TRUE 表示让密度曲线的填充颜色与点的颜色保持一致
p2 <- ggMarginal(p, type = "density", groupColour = TRUE, groupFill = TRUE, alpha=0.3)
# 显示最终的图片
print(p2)
# --- 步骤 6: 保存图片 (可选) ---
# 如果需要将图片保存为PDF文件，可以取消下面这行的注释
ggsave("Country_Clusters_Visualization.pdf", plot = p2, width = 25, height = 20)
library(tidyverse) # 包含 ggplot2，用于绘图
library(ggrepel)   # 用于防止标签重叠
library(RColorBrewer) # 提供美观的调色板
library(ggExtra)   # 用于绘制边缘密度图
# --- 步骤 1.5: 设置工作目录 (可选) ---
# 如果您的CSV文件不在R的默认工作目录下，请取消下面这行的注释并修改为您自己的路径
setwd("F:\\博士课题\\part1-协变量部分")
# --- 步骤 2: 读取你的数据 ---
# 请确保 "country_clusters.csv" 文件与你的 R 脚本在同一个工作目录下
df <- read.csv("country_clusters.csv")
# --- 步骤 3: 数据预处理 ---
# 将 Cluster 列转换为因子（factor）类型，这样 ggplot 才会把它当作分组变量来分配颜色
# 我们也过滤掉噪声点（标签为-1），因为它们不属于任何一个簇
df <- df %>%
filter(Cluster != -1) %>%
mutate(Cluster = as.factor(Cluster))
# --- 【修改】步骤 3.5: 定义手动颜色方案 ---
# 创建一个包含5种颜色的向量，为5个簇手动指定颜色
# 您可以根据喜好修改这些十六进制颜色代码
my_colors <- c("0" = "#E51C23", "1" = "#3F51B5", "2" = "#75C500", "3" = "#9C27B0", "4" = "#FF9800")
# --- 步骤 4: 绘制核心图形 ---
p <- ggplot(df, aes(x = TSNE_1, y = TSNE_2, color = Cluster, label = Country)) +
# 使用 geom_jitter 代替 geom_point，为重叠的点增加微小的随机扰动（弹性）
geom_jitter(width = 0.2, height = 0.2, size = 3, alpha = 0.8) +
# 在 geom_text_repel 中增加参数来增强“弹性”并去掉连线
geom_text_repel(
color = "black",
size =4,
max.overlaps = 20,      # 允许更多的标签重叠（实际上是让算法更努力地去放置它们）
force = 1.5,            # 增强标签之间的排斥力
segment.color = NA      # 将连线颜色设为NA，使其完全不可见
) +
# 【修改】: 使用 scale_color_manual 手动设置颜色
scale_color_manual(values = my_colors) +
# 将 alpha 设置为 0，使圈内背景完全透明
stat_ellipse(aes(color = Cluster), geom = "polygon", level = 0.95, alpha = 0) +
# 【修改】: 设置坐标轴和新的英文标题
labs(x = "t-SNE Component 1", y = "t-SNE Component 2", title = "t-SNE Visualization of Country Clusters (DBSCAN)") +
# 使用经典主题，并进行美化
theme_bw() +
theme(
axis.line = element_line(colour = "black"),
axis.title = element_text(color = "black", size = 14, face = "bold"),
axis.text = element_text(color = "black", size = 12),
plot.title = element_text(hjust = 0.5, size = 20, face = "bold"),
legend.position = "none" # 隐藏图例，让画面更干净
)
# --- 步骤 5: 添加边缘密度图并显示最终图片 ---
# type="density" 表示在边缘绘制密度曲线
# groupFill=TRUE 表示让密度曲线的填充颜色与点的颜色保持一致
p2 <- ggMarginal(p, type = "density", groupColour = TRUE, groupFill = TRUE, alpha=0.3)
# 显示最终的图片
print(p2)
# --- 步骤 6: 保存图片 (可选) ---
# 如果需要将图片保存为PDF文件，可以取消下面这行的注释
ggsave("Country_Clusters_Visualization.pdf", plot = p2, width = 25, height = 20)
library(tidyverse) # 包含 ggplot2，用于绘图
library(ggrepel)   # 用于防止标签重叠
library(RColorBrewer) # 提供美观的调色板
library(ggExtra)   # 用于绘制边缘密度图
# --- 步骤 1.5: 设置工作目录 (可选) ---
# 如果您的CSV文件不在R的默认工作目录下，请取消下面这行的注释并修改为您自己的路径
setwd("F:\\博士课题\\part1-协变量部分")
# --- 步骤 2: 读取你的数据 ---
# 请确保 "country_clusters.csv" 文件与你的 R 脚本在同一个工作目录下
df <- read.csv("country_clusters.csv")
# --- 步骤 3: 数据预处理 ---
# 将 Cluster 列转换为因子（factor）类型，这样 ggplot 才会把它当作分组变量来分配颜色
# 我们也过滤掉噪声点（标签为-1），因为它们不属于任何一个簇
df <- df %>%
filter(Cluster != -1) %>%
mutate(Cluster = as.factor(Cluster))
# --- 【修改】步骤 3.5: 定义手动颜色方案 ---
# 创建一个包含5种颜色的向量，为5个簇手动指定颜色
# 您可以根据喜好修改这些十六进制颜色代码
my_colors <- c("0" = "#E51C23", "1" = "#3F51B5", "2" = "#75C500", "3" = "#9C27B0", "4" = "#FF9800")
# --- 步骤 4: 绘制核心图形 ---
p <- ggplot(df, aes(x = TSNE_1, y = TSNE_2, color = Cluster, label = Country)) +
# 使用 geom_jitter 代替 geom_point，为重叠的点增加微小的随机扰动（弹性）
geom_jitter(width = 0.2, height = 0.2, size = 3, alpha = 0.8) +
# 在 geom_text_repel 中增加参数来增强“弹性”并去掉连线
geom_text_repel(
color = "black",
size =4,
max.overlaps = 20,      # 允许更多的标签重叠（实际上是让算法更努力地去放置它们）
force = 1.5,            # 增强标签之间的排斥力
segment.color = NA      # 将连线颜色设为NA，使其完全不可见
) +
# 【修改】: 使用 scale_color_manual 手动设置颜色
scale_color_manual(values = my_colors) +
# 将 alpha 设置为 0，使圈内背景完全透明
stat_ellipse(aes(color = Cluster), geom = "polygon", level = 0.95, alpha = 0) +
# 【修改】: 设置坐标轴和新的英文标题
labs(x = "t-SNE Component 1", y = "t-SNE Component 2", title = "t-SNE Visualization of Country Clusters (DBSCAN)") +
# 使用经典主题，并进行美化
theme_bw() +
theme(
axis.line = element_line(colour = "black"),
axis.title = element_text(color = "black", size = 14, face = "bold"),
axis.text = element_text(color = "black", size = 12),
plot.title = element_text(hjust = 0.5, size = 20, face = "bold"),
legend.position = "none" # 隐藏图例，让画面更干净
)
# --- 步骤 5: 添加边缘密度图并显示最终图片 ---
# type="density" 表示在边缘绘制密度曲线
# groupFill=TRUE 表示让密度曲线的填充颜色与点的颜色保持一致
p2 <- ggMarginal(p, type = "density", groupColour = TRUE, groupFill = TRUE, alpha=0.5)
# 显示最终的图片
print(p2)
# --- 步骤 6: 保存图片 (可选) ---
# 如果需要将图片保存为PDF文件，可以取消下面这行的注释
ggsave("Country_Clusters_Visualization.pdf", plot = p2, width = 25, height = 20)
library(tidyverse) # 包含 ggplot2，用于绘图
library(ggrepel)   # 用于防止标签重叠
library(RColorBrewer) # 提供美观的调色板
library(ggExtra)   # 用于绘制边缘密度图
# --- 步骤 1.5: 设置工作目录 (可选) ---
# 如果您的CSV文件不在R的默认工作目录下，请取消下面这行的注释并修改为您自己的路径
setwd("F:\\博士课题\\part1-协变量部分")
# --- 步骤 2: 读取你的数据 ---
# 请确保 "country_clusters.csv" 文件与你的 R 脚本在同一个工作目录下
df <- read.csv("country_clusters.csv")
# --- 步骤 3: 数据预处理 ---
# 将 Cluster 列转换为因子（factor）类型，这样 ggplot 才会把它当作分组变量来分配颜色
# 我们也过滤掉噪声点（标签为-1），因为它们不属于任何一个簇
df <- df %>%
filter(Cluster != -1) %>%
mutate(Cluster = as.factor(Cluster))
# --- 【修改】步骤 3.5: 定义手动颜色方案 ---
# 创建一个包含5种颜色的向量，为5个簇手动指定颜色
# 您可以根据喜好修改这些十六进制颜色代码
my_colors <- c("0" = "#E51C23", "1" = "#3F51B5", "2" = "#75C500", "3" = "#9C27B0", "4" = "#FF9800")
# --- 步骤 4: 绘制核心图形 ---
p <- ggplot(df, aes(x = TSNE_1, y = TSNE_2, color = Cluster, label = Country)) +
# 使用 geom_jitter 代替 geom_point，为重叠的点增加微小的随机扰动（弹性）
geom_jitter(width = 0.2, height = 0.2, size = 3, alpha = 1) +
# 在 geom_text_repel 中增加参数来增强“弹性”并去掉连线
geom_text_repel(
color = "black",
size =4,
max.overlaps = 20,      # 允许更多的标签重叠（实际上是让算法更努力地去放置它们）
force = 1.5,            # 增强标签之间的排斥力
segment.color = NA      # 将连线颜色设为NA，使其完全不可见
) +
# 【修改】: 使用 scale_color_manual 手动设置颜色
scale_color_manual(values = my_colors) +
# 将 alpha 设置为 0，使圈内背景完全透明
stat_ellipse(aes(color = Cluster), geom = "polygon", level = 0.95, alpha = 0) +
# 【修改】: 设置坐标轴和新的英文标题
labs(x = "t-SNE Component 1", y = "t-SNE Component 2", title = "t-SNE Visualization of Country Clusters (DBSCAN)") +
# 使用经典主题，并进行美化
theme_bw() +
theme(
axis.line = element_line(colour = "black"),
axis.title = element_text(color = "black", size = 14, face = "bold"),
axis.text = element_text(color = "black", size = 12),
plot.title = element_text(hjust = 0.5, size = 20, face = "bold"),
legend.position = "none" # 隐藏图例，让画面更干净
)
# --- 步骤 5: 添加边缘密度图并显示最终图片 ---
# type="density" 表示在边缘绘制密度曲线
# groupFill=TRUE 表示让密度曲线的填充颜色与点的颜色保持一致
p2 <- ggMarginal(p, type = "density", groupColour = TRUE, groupFill = TRUE, alpha=0.5)
# 显示最终的图片
print(p2)
# --- 步骤 6: 保存图片 (可选) ---
# 如果需要将图片保存为PDF文件，可以取消下面这行的注释
ggsave("Country_Clusters_Visualization.pdf", plot = p2, width = 25, height = 20)
library(tidyverse) # 包含 ggplot2，用于绘图
library(ggrepel)   # 用于防止标签重叠
library(RColorBrewer) # 提供美观的调色板
library(ggExtra)   # 用于绘制边缘密度图
# --- 步骤 1.5: 设置工作目录 (可选) ---
# 如果您的CSV文件不在R的默认工作目录下，请取消下面这行的注释并修改为您自己的路径
setwd("F:\\博士课题\\part1-协变量部分")
# --- 步骤 2: 读取你的数据 ---
# 请确保 "country_clusters.csv" 文件与你的 R 脚本在同一个工作目录下
df <- read.csv("country_clusters.csv")
# --- 步骤 3: 数据预处理 ---
# 将 Cluster 列转换为因子（factor）类型，这样 ggplot 才会把它当作分组变量来分配颜色
# 我们也过滤掉噪声点（标签为-1），因为它们不属于任何一个簇
df <- df %>%
filter(Cluster != -1) %>%
mutate(Cluster = as.factor(Cluster))
# --- 【修改】步骤 3.5: 定义手动颜色方案 ---
# 创建一个包含5种颜色的向量，为5个簇手动指定颜色
# 您可以根据喜好修改这些十六进制颜色代码
my_colors <- c("0" = "#E51C23", "1" = "#3F51B5", "2" = "#75C500", "3" = "#9C27B0", "4" = "#FF9800")
# --- 步骤 4: 绘制核心图形 ---
p <- ggplot(df, aes(x = TSNE_1, y = TSNE_2, color = Cluster, label = Country)) +
# 使用 geom_jitter 代替 geom_point，为重叠的点增加微小的随机扰动（弹性）
geom_jitter(width = 0.2, height = 0.2, size = 4, alpha = 1) +
# 在 geom_text_repel 中增加参数来增强“弹性”并去掉连线
geom_text_repel(
color = "black",
size =4,
max.overlaps = 20,      # 允许更多的标签重叠（实际上是让算法更努力地去放置它们）
force = 1.5,            # 增强标签之间的排斥力
segment.color = NA      # 将连线颜色设为NA，使其完全不可见
) +
# 【修改】: 使用 scale_color_manual 手动设置颜色
scale_color_manual(values = my_colors) +
# 将 alpha 设置为 0，使圈内背景完全透明
stat_ellipse(aes(color = Cluster), geom = "polygon", level = 0.95, alpha = 0) +
# 【修改】: 设置坐标轴和新的英文标题
labs(x = "t-SNE Component 1", y = "t-SNE Component 2", title = "t-SNE Visualization of Country Clusters (DBSCAN)") +
# 使用经典主题，并进行美化
theme_bw() +
theme(
axis.line = element_line(colour = "black"),
axis.title = element_text(color = "black", size = 14, face = "bold"),
axis.text = element_text(color = "black", size = 12),
plot.title = element_text(hjust = 0.5, size = 20, face = "bold"),
legend.position = "none" # 隐藏图例，让画面更干净
)
# --- 步骤 5: 添加边缘密度图并显示最终图片 ---
# type="density" 表示在边缘绘制密度曲线
# groupFill=TRUE 表示让密度曲线的填充颜色与点的颜色保持一致
p2 <- ggMarginal(p, type = "density", groupColour = TRUE, groupFill = TRUE, alpha=0.5)
# 显示最终的图片
print(p2)
# --- 步骤 6: 保存图片 (可选) ---
# 如果需要将图片保存为PDF文件，可以取消下面这行的注释
ggsave("Country_Clusters_Visualization.pdf", plot = p2, width = 25, height = 20)
library(tidyverse) # 包含 ggplot2，用于绘图
library(ggrepel)   # 用于防止标签重叠
library(RColorBrewer) # 提供美观的调色板
library(ggExtra)   # 用于绘制边缘密度图
# --- 步骤 1.5: 设置工作目录 (可选) ---
# 如果您的CSV文件不在R的默认工作目录下，请取消下面这行的注释并修改为您自己的路径
setwd("F:\\博士课题\\part1-协变量部分")
# --- 步骤 2: 读取你的数据 ---
# 请确保 "country_clusters.csv" 文件与你的 R 脚本在同一个工作目录下
df <- read.csv("country_clusters.csv")
# --- 步骤 3: 数据预处理 ---
# 将 Cluster 列转换为因子（factor）类型，这样 ggplot 才会把它当作分组变量来分配颜色
# 我们也过滤掉噪声点（标签为-1），因为它们不属于任何一个簇
df <- df %>%
filter(Cluster != -1) %>%
mutate(Cluster = as.factor(Cluster))
# --- 【修改】步骤 3.5: 定义手动颜色方案 ---
# 创建一个包含5种颜色的向量，为5个簇手动指定颜色
# 您可以根据喜好修改这些十六进制颜色代码
my_colors <- c("0" = "#E51C23", "1" = "#3F51B5", "2" = "#75C500", "3" = "#9C27B0", "4" = "#FF9800")
# --- 步骤 4: 绘制核心图形 ---
p <- ggplot(df, aes(x = TSNE_1, y = TSNE_2, color = Cluster, label = Country)) +
# 使用 geom_jitter 代替 geom_point，为重叠的点增加微小的随机扰动（弹性）
geom_jitter(width = 0.2, height = 0.2, size = 4, alpha = 1) +
# 在 geom_text_repel 中增加参数来增强“弹性”并去掉连线
geom_text_repel(
color = "black",
size =4,
max.overlaps = 20,      # 允许更多的标签重叠（实际上是让算法更努力地去放置它们）
force = 1.5,            # 增强标签之间的排斥力
segment.color = NA      # 将连线颜色设为NA，使其完全不可见
) +
# 【修改】: 使用 scale_color_manual 手动设置颜色
scale_color_manual(values = my_colors) +
# 将 alpha 设置为 0，使圈内背景完全透明
stat_ellipse(aes(color = Cluster), geom = "polygon", level = 0.95, alpha = 0) +
# 【修改】: 设置坐标轴和新的英文标题
labs(x = "t-SNE Component 1", y = "t-SNE Component 2", title = "t-SNE Visualization of Country Clusters (DBSCAN)") +
# 使用经典主题，并进行美化
theme_bw() +
theme(
axis.line = element_line(colour = "black"),
axis.title = element_text(color = "black", size = 14, face = "bold"),
axis.text = element_text(color = "black", size = 12),
plot.title = element_text(hjust = 0.5, size = 20, face = "bold"),
legend.position = "none" # 隐藏图例，让画面更干净
)
# --- 步骤 5: 添加边缘密度图并显示最终图片 ---
# type="density" 表示在边缘绘制密度曲线
# groupFill=TRUE 表示让密度曲线的填充颜色与点的颜色保持一致
p2 <- ggMarginal(p, type = "density", groupColour = TRUE, groupFill = TRUE, alpha=0.3)
# 显示最终的图片
print(p2)
# --- 步骤 6: 保存图片 (可选) ---
# 如果需要将图片保存为PDF文件，可以取消下面这行的注释
ggsave("Country_Clusters_Visualization.pdf", plot = p2, width = 25, height = 20)
library(tidyverse) # 包含 ggplot2，用于绘图
library(ggrepel)   # 用于防止标签重叠
library(RColorBrewer) # 提供美观的调色板
library(ggExtra)   # 用于绘制边缘密度图
# --- 步骤 1.5: 设置工作目录 (可选) ---
# 如果您的CSV文件不在R的默认工作目录下，请取消下面这行的注释并修改为您自己的路径
setwd("F:\\博士课题\\part1-协变量部分")
# --- 步骤 2: 读取你的数据 ---
# 请确保 "country_clusters.csv" 文件与你的 R 脚本在同一个工作目录下
df <- read.csv("country_clusters.csv")
# --- 步骤 3: 数据预处理 ---
# 将 Cluster 列转换为因子（factor）类型，这样 ggplot 才会把它当作分组变量来分配颜色
# 我们也过滤掉噪声点（标签为-1），因为它们不属于任何一个簇
df <- df %>%
filter(Cluster != -1) %>%
mutate(Cluster = as.factor(Cluster))
# --- 【修改】步骤 3.5: 定义手动颜色方案 ---
# 创建一个包含5种颜色的向量，为5个簇手动指定颜色
# 您可以根据喜好修改这些十六进制颜色代码
my_colors <- c("0" = "#E51C23", "1" = "#3F51B5", "2" = "#75C500", "3" = "#9C27B0", "4" = "#FF9800")
# --- 步骤 4: 绘制核心图形 ---
p <- ggplot(df, aes(x = TSNE_1, y = TSNE_2, color = Cluster, label = Country)) +
# 使用 geom_jitter 代替 geom_point，为重叠的点增加微小的随机扰动（弹性）
geom_jitter(width = 0.2, height = 0.2, size = 4, alpha = 1) +
# 在 geom_text_repel 中增加参数来增强“弹性”并去掉连线
geom_text_repel(
color = "black",
size =4,
max.overlaps = 20,      # 允许更多的标签重叠（实际上是让算法更努力地去放置它们）
force = 1.5,            # 增强标签之间的排斥力
segment.color = NA      # 将连线颜色设为NA，使其完全不可见
) +
# 【修改】: 使用 scale_color_manual 手动设置颜色
scale_color_manual(values = my_colors) +
# 将 alpha 设置为 0，使圈内背景完全透明
stat_ellipse(aes(color = Cluster), geom = "polygon", level = 0.95, alpha = 0) +
# 【修改】: 设置坐标轴和新的英文标题
labs(x = "t-SNE Component 1", y = "t-SNE Component 2", title = "t-SNE Visualization of Country Clusters (DBSCAN)") +
# 使用经典主题，并进行美化
theme_bw() +
theme(
axis.line = element_line(colour = "black"),
axis.title = element_text(color = "black", size = 14, face = "bold"),
axis.text = element_text(color = "black", size = 12),
plot.title = element_text(hjust = 0.5, size = 20, face = "bold"),
legend.position = "none" # 隐藏图例，让画面更干净
)
# --- 步骤 5: 添加边缘密度图并显示最终图片 ---
# type="density" 表示在边缘绘制密度曲线
# groupFill=TRUE 表示让密度曲线的填充颜色与点的颜色保持一致
p2 <- ggMarginal(p, type = "density", groupColour = TRUE, groupFill = TRUE, alpha=0.4)
# 显示最终的图片
print(p2)
# --- 步骤 6: 保存图片 (可选) ---
# 如果需要将图片保存为PDF文件，可以取消下面这行的注释
ggsave("Country_Clusters_Visualization.pdf", plot = p2, width = 25, height = 20)
