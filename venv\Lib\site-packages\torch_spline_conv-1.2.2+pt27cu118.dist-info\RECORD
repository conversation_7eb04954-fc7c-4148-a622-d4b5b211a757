torch_spline_conv-1.2.2+pt27cu118.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
torch_spline_conv-1.2.2+pt27cu118.dist-info/METADATA,sha256=5qzPd8WeUhzo8A1Tbn7ZtXdKl4xe2gkDuwHAShMAEj4,9446
torch_spline_conv-1.2.2+pt27cu118.dist-info/RECORD,,
torch_spline_conv-1.2.2+pt27cu118.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
torch_spline_conv-1.2.2+pt27cu118.dist-info/WHEEL,sha256=XkFE14KmFh7mutkkb-qn_ueuH2lwfT8rLdfc5xpQ7wE,99
torch_spline_conv-1.2.2+pt27cu118.dist-info/licenses/LICENSE,sha256=CwL9rVVusWjlenTl90POSLEgd6J4GS-qdUuYAAi8_2Q,1105
torch_spline_conv-1.2.2+pt27cu118.dist-info/top_level.txt,sha256=1k6x63sUWmbB2FOJ0dF8xVNnTYXTqT0Ulwx2a8bfetw,18
torch_spline_conv/__init__.py,sha256=cvCBMUDEOBpd2SBqU-_xXH1_5DYxg3qW4TRXv9q0zrg,1641
torch_spline_conv/__pycache__/__init__.cpython-39.pyc,,
torch_spline_conv/__pycache__/basis.cpython-39.pyc,,
torch_spline_conv/__pycache__/conv.cpython-39.pyc,,
torch_spline_conv/__pycache__/testing.cpython-39.pyc,,
torch_spline_conv/__pycache__/weighting.cpython-39.pyc,,
torch_spline_conv/_basis_cpu.pyd,sha256=M4pUiclLMKjzn4Y8C-KKvlefMEnxkgqJ_cxiEzf85hI,237568
torch_spline_conv/_basis_cuda.pyd,sha256=M2EaoxXqLR8di-ky_zFMfUCO-XFQ9pAsNrfuEljrnSY,466944
torch_spline_conv/_version_cpu.pyd,sha256=1ki-0eq7bf31OhHSCRe0c0yNY29u6SQiFSxWYyleyqU,95744
torch_spline_conv/_version_cuda.pyd,sha256=I2_fGSlaHnPmSkbKvxQT9hE-NS40HOhnjK6Wotb_Cfg,95744
torch_spline_conv/_weighting_cpu.pyd,sha256=NeJa2NGLdz21bOEMjLD27NbU_Jg_4kSlIAFhI7234YI,218624
torch_spline_conv/_weighting_cuda.pyd,sha256=MVQslZ6pMibwU2ccxwqvrKP7V9sSiAkPnYQpNrs13FQ,314368
torch_spline_conv/basis.py,sha256=WWho65LI2myMJqvYXNUqdQxt4adPe5G3o1CP2fwoXPE,369
torch_spline_conv/conv.py,sha256=IkDAxC_arjpsZ5pNqQ8EcDgfIqH4y7s8In6iz3A_o1M,3291
torch_spline_conv/testing.py,sha256=kO_ePW-1TJR5a-zrXeNwsk3-ztE_AHhW6-6PMmk8gJ4,349
torch_spline_conv/weighting.py,sha256=FOvCqsfm75t8rVNsi4n9lwagjQHSwv8qH5VpvkNd-PU,268
